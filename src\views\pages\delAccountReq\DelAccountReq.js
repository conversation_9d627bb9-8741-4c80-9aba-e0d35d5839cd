/* eslint-disable react/prop-types */
import React, { useState } from 'react'
import {
  CButton,
  CForm,
  CFormLabel,
  CFormInput,
  CFormTextarea,
  CSpinner,
  CImage,
  CCard,
  CCardHeader,
  CCardBody,
} from '@coreui/react'
import Joi from 'joi'
import { useForm } from 'react-hook-form'
import { addDoc, collection, getDocs, query, where } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import Swal from 'sweetalert2'
import { joiResolver } from '@hookform/resolvers/joi'
import { toast } from 'react-toastify'
import Logo from '../../../assets/images/logo.png'

const reqSchema = Joi.object({
  name: Joi.string().empty().required().messages({
    'string.empty': 'Name is required.',
    'string.required': 'Name is required.',
  }),
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .messages({
      'string.empty': 'Email is required.',
      'string.required': 'Email is required.',
      'string.email': 'Email must be valid.',
    }),
  comment: Joi.string().required().messages({
    'string.empty': 'Comment is required.',
    'string.required': 'Comment is required.',
  }),
})

const DelAccountReq = () => {
  const [isLoading, setLoading] = useState(false)
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: '',
      email: '',
      comment: '',
    },
    resolver: joiResolver(reqSchema),
    mode: 'onSubmit',
  })

  async function submitForm(formData) {
    setLoading(true)
    let q = query(collection(db, 'Users'), where('email', '==', formData?.email))
    await getDocs(q)
      .then(async (response) => {
        // USER FOUND AS REGISTERED
        if (response.docs.length) {
          // IF USER ALREADY SUBMITTED THE REQUEST
          let q2 = query(collection(db, 'DeleteRequest'), where('email', '==', formData?.email))
          await getDocs(q2).then((resp) => {
            if (resp.docs.length) {
              setLoading(false)
              reset()
              return Swal.fire({
                title: 'Request Already Sent',
                text: 'Your request to delete the account is already submitted.',
              })
            }
          })

          // NEW REQUEST
          await addDoc(collection(db, 'DeleteRequest'), {
            userId: response.docs[0]?.id,
            firstName: response.docs[0]?.data()?.firstName,
            surName: response.docs[0]?.data()?.surName,
            email: response.docs[0]?.data()?.email,
            comment: formData?.comment,
          }).then(() => {
            Swal.fire({
              title: 'Request Sent',
              text: 'Your request to delete the account has been sent.',
            })
          })
          setLoading(false)
          reset()
        }
        // USER NOT FOUND
        if (response.docs.length === 0) {
          toast.error('Email not found.')
          setLoading(false)
        }
      })
      .catch((error) => {
        toast.error('Something went wrong while sending request.')
        console.log('ERROR -', error.code)
        setLoading(false)
      })
  }
  return (
    <>
      <div className="vw-100 vh-100 d-flex justify-content-center align-items-center">
        <CCard className="req-card">
          <CCardHeader>
            <p className="mx-auto my-0" style={{ width: 210 }}>
              <CImage src={Logo} className="mx-auto" style={{ width: 200 }} />
            </p>
          </CCardHeader>
          <CCardBody>
            <CForm onSubmit={handleSubmit(submitForm)}>
              <div className="mb-3">
                <CFormLabel htmlFor="name">Name</CFormLabel>
                <CFormInput type="text" id="name" {...register('name')} />
                {errors?.name ? <p className="mb-0 text-danger">{errors?.name?.message}</p> : null}
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="email">Email</CFormLabel>
                <CFormInput type="email" id="email" {...register('email')} />
                {errors?.email ? <p className="text-danger">{errors?.email.message}</p> : null}
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="comment">Comment</CFormLabel>
                <CFormTextarea
                  {...register('comment')}
                  id="comment"
                  rows={4}
                  placeholder="Why are you deleting your account permanently?"
                />
                {errors?.comment ? <p className="text-danger">{errors?.comment.message}</p> : null}
              </div>
              <div className="text-end">
                <CButton type="submit" disabled={isLoading}>
                  Submit
                  {isLoading ? <CSpinner size="sm" className="ms-2" /> : null}
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </div>
    </>
  )
}

export default DelAccountReq
