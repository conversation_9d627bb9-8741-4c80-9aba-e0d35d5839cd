/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react'
import {
  <PERSON>utton,
  <PERSON>pinner,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
} from '@coreui/react'
import Swal from 'sweetalert2'
import { toast } from 'react-toastify'
import { collection, deleteDoc, doc, getDoc, getDocs } from 'firebase/firestore'
import { auth, db } from 'src/firebase/firebase'
import { cilTrash } from '@coreui/icons'
import CIcon from '@coreui/icons-react'
import EmptyBox from 'src/components/EmptyBox'

const Requests = () => {
  const [requests, setRequests] = useState([])
  const [isLoading, setLoading] = useState(true)

  // GET ALL REQUESTS
  async function getAllRequests() {
    setLoading(true)
    const arrOfRequests = []
    const categoriesDocs = await getDocs(collection(db, 'DeleteRequest'))
    categoriesDocs.forEach((request) => {
      arrOfRequests.push({ id: request.id, ...request.data() })
    })

    setRequests(arrOfRequests)
    setLoading(false)
  }

  // DELETE THIS USER
  async function deleteThisUser(request) {
    Swal.fire({
      title: 'Delete?',
      text: 'Are you sure to delete this user? He/She would be unable to use the app anymore.',
      icon: 'question',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Confirm',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        setLoading(true)
        getDoc(doc(db, 'Users', request?.userId))
          .then((response) => {
            if (response.exists()) {
              deleteDoc(doc(db, 'Users', request?.userId))
                .then(() => toast.success('User deleted successfully.'))
                .catch((error) => toast.error('Something went wrong while deleting the user.'))
              deleteDoc(doc(db, 'Requests', request?.id))
              getAllRequests()
            }
          })
          .catch((error) => toast.error('Something went wrong while deleting the user.'))
      }
    })
  }
  useEffect(() => {
    getAllRequests()
  }, [])
  if (isLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )
  return (
    <>
      <h4>Delete Requests</h4>

      {/* TABLE */}
      {requests.length ? (
        <CTable style={{ verticalAlign: 'middle' }}>
          <CTableHead>
            <CTableRow>
              <CTableHeaderCell scope="col">#</CTableHeaderCell>
              <CTableHeaderCell scope="col">Name</CTableHeaderCell>
              <CTableHeaderCell scope="col">Email</CTableHeaderCell>
              <CTableHeaderCell scope="col">Comment</CTableHeaderCell>
              <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {requests?.map((item, i) => {
              return (
                <CTableRow key={i}>
                  <CTableHeaderCell scope="row">{i + 1}</CTableHeaderCell>
                  <CTableDataCell>
                    {item?.firstName}
                    {item.surName}
                  </CTableDataCell>
                  <CTableDataCell>{item?.email}</CTableDataCell>
                  <CTableDataCell>{item?.comment}</CTableDataCell>
                  <CTableDataCell className="action-buttons">
                    <CButton
                      color="danger"
                      size="sm"
                      className="ms-1"
                      onClick={() => deleteThisUser(item)}
                    >
                      <CIcon icon={cilTrash} className="text-light" />
                    </CButton>
                  </CTableDataCell>
                </CTableRow>
              )
            })}
          </CTableBody>
        </CTable>
      ) : (
        <EmptyBox label={'No requests yet..'} />
      )}
    </>
  )
}

export default Requests
