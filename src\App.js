import React, { Suspense } from 'react'
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import './scss/style.scss'

const loading = (
  <div className="pt-3 text-center">
    <div className="sk-spinner sk-spinner-pulse"></div>
  </div>
)

// Containers
const DefaultLayout = React.lazy(() => import('./layout/DefaultLayout'))

// Pages
const Login = React.lazy(() => import('./views/pages/login/Login'))
const Register = React.lazy(() => import('./views/pages/register/Register'))
const Page404 = React.lazy(() => import('./views/pages/page404/Page404'))
const Page500 = React.lazy(() => import('./views/pages/page500/Page500'))
const DelAccountReq = React.lazy(() => import('./views/pages/delAccountReq/DelAccountReq'))
const Support = React.lazy(() => import('./views/pages/support/Support'))
const PrivacyPolicy = React.lazy(() => import('./views/privacy-policy'))

const App = () => {
  const userId = localStorage.getItem('userId')
  const userData = JSON.parse(localStorage.getItem('userData'))

  return (
    <BrowserRouter>
      <ToastContainer
        autoClose={4000}
        position="top-right"
        pauseOnHover={false}
        draggable={false}
        closeButton={false}
      />
      <Suspense fallback={loading}>
        <Routes>
          <Route exact path="/" name="Navigate to Login" element={<Navigate to={'/login'} />} />
          <Route
            exact
            path="/login"
            name="Login Page"
            element={userId && userData?.isAdmin ? <Navigate to={'/users'} /> : <Login />}
          />
          <Route exact path="/register" name="Register Page" element={<Register />} />
          <Route
            exact
            path="/delete-account-request"
            name="Del Account Req"
            element={<DelAccountReq />}
          />
          <Route exact path="/support" name="Support" element={<Support />} />
          <Route exact path="/privacy-policy" name="Privacy Policy" element={<PrivacyPolicy />} />
          <Route exact path="/404" name="Page 404" element={<Page404 />} />
          <Route exact path="/500" name="Page 500" element={<Page500 />} />
          <Route path="*" name="Home" element={<DefaultLayout />} />
        </Routes>
      </Suspense>
    </BrowserRouter>
  )
}

export default App
